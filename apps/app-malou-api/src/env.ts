/* eslint-disable @typescript-eslint/no-var-requires */
import chalk from 'chalk';
import path from 'path';

console.info("WARNING ! Don't forget to import src/env.ts before any package models import");
if (!process.env.NODE_ENV) {
    throw new Error('!!! NO ENVIRONMENT SET !!!');
}

require('dotenv').config({ path: path.resolve(__dirname, `../.env.${process.env.NODE_ENV}`) });
require('dotenv').config({
    path: path.resolve(__dirname, '../.env.default'),
});

console.log(`Running on env: ${process.env.NODE_ENV}`);

const isProductionQueueUrlPresent = Object.keys(process.env)
    .filter((key) => key.includes('_QUEUE_URL'))
    .map((key) => process.env[key])
    .some((url) => url?.includes('production'));

const isDeveloperConsumingProductionMessages =
    process.env.I_AM_A === 'developer' && isProductionQueueUrlPresent && process.env.START_SQS_CONSUMER === 'true';

const isDeveloperProcessingProductionJobs =
    process.env.I_AM_A === 'developer' &&
    process.env.MONGODB_AGENDA_URI?.includes('omni_production') &&
    process.env.START_AGENDA_CONSUMER === 'true';

const isDeveloperConsumingPubSubMessages = process.env.I_AM_A === 'developer' && process.env.START_PUBSUB_SUBSCRIPTION === 'true';

if (isDeveloperProcessingProductionJobs || isDeveloperConsumingProductionMessages || isDeveloperConsumingPubSubMessages) {
    console.log(
        chalk.bold.red('WARNING: You cannot process production jobs, queues or pub/sub messages. Please check your configuration.')
    );
    // process.exit(1);
}
