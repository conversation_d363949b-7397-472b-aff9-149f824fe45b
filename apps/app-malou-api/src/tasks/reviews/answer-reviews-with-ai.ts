import 'reflect-metadata';

import ':env';

import { container } from 'tsyringe';

import { IReview, ReviewModel } from '@malou-io/package-models';

import { AutoReplyUseCases } from ':modules/automations/auto-reply.use-cases';
import ':plugins/db';

const autoReplyUseCases = container.resolve(AutoReplyUseCases);

async function main() {
    // List of review IDs to answer with AI
    // Replace this array with the actual review IDs you want to process
    const reviewIds: string[] = [
        '68a496b2c00cca4cec7ee55b',
        '68a48bf9c00cca4cec7d6714',
        '68a460c9c00cca4cec79a8e0',
        '68a44e5bc00cca4cec7698f6',
        '68a38699c00cca4cec4f24d3',
        '68a37383c00cca4cec4d3399',
        '68a23ef0c00cca4cecfa1fd8',
        '68a22bcdc00cca4cecf82f50',
        '68a1ba4cc00cca4cecebefa5',
        '68a1b537c00cca4ceceb4228',
        '68a1ac6bc00cca4cecea39ab',
        '68a0f5f9c00cca4cecc1f733',
        '68a0e231c00cca4cecbecd90',
        '68a0e1b4c00cca4cecbeae85',
        '68a0d550c00cca4cecbcef0f',
        '689f8550c00cca4cec84495e',
        '689f51adc00cca4cec7f9f67',
        '689f4f2bc00cca4cec7f6653',
        '689f3c8cc00cca4cec7de194',
        '689f28d0c00cca4cec7c094a',
        '689e3225c00cca4cec4e5dd8',
        '689cd59bc00cca4cec181f68',
        '689c8d48c00cca4cec1168a7',
        '689c73cdc00cca4cec0e88b8',
        '689c6dd2c00cca4cec0d2317',
        '689c6c39c00cca4cec0cfa1b',
        '689bb67ec00cca4cecdf21da',
        '689b9799c00cca4cecdc0685',
        '689b8a07c00cca4cecda201d',
        '689b7d31c00cca4cecd7a769',
        '689b33cbc00cca4ceccca4e1',
        '689b1cdec00cca4cecca3c9b',
        '689afac3cfd650767c238f2b',
        '689afac3cfd650767c238f66',
        '689afac3cfd650767c238f50',
        '689afac3cfd650767c238f76',
        '689afac3cfd650767c238f37',
        '689afac3cfd650767c238f19',
        '689afac3cfd650767c238f15',
        '689afac3cfd650767c238f17',
        '689afac3cfd650767c238f27',
        '689afac3cfd650767c238f11',
        '689afac3cfd650767c238f84',
        '689afac3cfd650767c238fd0',
        '689afac3cfd650767c238f0b',
        '689afac3cfd650767c238f1f',
        '689afac3cfd650767c238f80',
        '689afac3cfd650767c238f25',
        '689afac3cfd650767c238fc0',
        '689afac3cfd650767c238f58',
        // '68afb90f169bde6943235909',
        // Add your review IDs here
        // Example:
        // '507f1f77bcf86cd799439011',
        // '507f1f77bcf86cd799439012',
        // '507f1f77bcf86cd799439013',
    ];

    if (!reviewIds.length) {
        console.log('No review IDs provided. Please add review IDs to the reviewIds array.');
        process.exit(1);
    }

    console.log(`About to answer ${reviewIds.length} reviews with AI`);

    // Fetch reviews from database
    const reviews = await ReviewModel.find({ _id: { $in: reviewIds } }, {}, { lean: true });

    if (reviews.length !== reviewIds.length) {
        console.log(`Warning: Found ${reviews.length} reviews out of ${reviewIds.length} requested`);
        const foundIds = reviews.map((r) => r._id.toString());
        const missingIds = reviewIds.filter((id) => !foundIds.includes(id));
        console.log('Missing review IDs:', missingIds);
    }

    // Process reviews in batches
    const batchSize = 10;
    let okCount = 0;
    let koCount = 0;
    let processedCount = 0;

    console.log('Starting to process reviews...');

    for (let i = 0; i < reviews.length; i += batchSize) {
        const batch = reviews.slice(i, i + batchSize);

        console.log(`Processing batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(reviews.length / batchSize)}`);
        console.log(`Reviews ${i + 1}-${Math.min(i + batchSize, reviews.length)} of ${reviews.length}`);

        const promises = batch.map(async (review: IReview) => {
            try {
                const result = await autoReplyUseCases.handleReviewAutoReply(review);
                return result ? 'success' : 'skipped';
            } catch (error) {
                console.error(`Error processing review ${review._id}:`, error);
                return 'error';
            }
        });

        const results = await Promise.all(promises);

        const batchOkCount = results.filter((r) => r === 'success').length;
        const batchSkippedCount = results.filter((r) => r === 'skipped').length;
        const batchErrorCount = results.filter((r) => r === 'error').length;

        okCount += batchOkCount;
        koCount += batchSkippedCount + batchErrorCount;
        processedCount += batch.length;

        console.log(`Batch completed - Success: ${batchOkCount}, Skipped: ${batchSkippedCount}, Errors: ${batchErrorCount}`);
        console.log(`Total progress: ${processedCount}/${reviews.length}`);
        console.log('---');

        // Small delay between batches to avoid overwhelming the system
        if (i + batchSize < reviews.length) {
            await new Promise((resolve) => setTimeout(resolve, 1000));
        }
    }

    console.log('=== FINAL RESULTS ===');
    console.log(`Total reviews processed: ${processedCount}`);
    console.log(`Successfully scheduled for AI reply: ${okCount}`);
    console.log(`Skipped or failed: ${koCount}`);
    console.log('===================');
}

main()
    .then(() => {
        console.log('Task completed successfully');
        process.exit(0);
    })
    .catch((error) => {
        console.error('Task failed:', error);
        process.exit(1);
    });
